import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:recallloop/features/auth/data/model/login/login_model.dart';
import 'package:recallloop/features/auth/data/model/signup/signup_model.dart';
import 'package:recallloop/features/auth/data/services/login_service.dart';
import 'package:recallloop/features/auth/data/services/signup_service.dart';

part 'auth_bloc.freezed.dart';
part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(const _Initial()) {
    on<AuthEvent>((event, emit) async {
      try {
        if (event is _LoginRequested) {
          emit(const _Loading());

          final loginService = LoginService();
          final loginModel = await loginService.login(
            event.email,
            event.password,
          );

          if (loginModel != null) {
            emit(_Authenticated(loginUser: loginModel));
          } else {
            emit(_Error('Invalid email or password'));
          }
        }

        if (event is _SignUpRequested) {
          emit(const _Loading());
          print(event);
          final signupService = SignupService();
          final signupModel = await signupService.signup(
            event.email,
            event.password,
            event.firstName,
            event.lastName,
            event.phone,
            event.username,
          );
          print(signupModel);
          if (signupModel != null) {
            emit(_SignUpSuccess(signupUser: signupModel));
          } else {
            emit(
              _SignUpError(
                'Sign up failed. Please check your information and try again.',
              ),
            );
          }
        }

        if (event is _LogoutRequested) {
          emit(const _Loading());

          // TODO: Implement actual logout logic (clear tokens, etc.)
          emit(const _Unauthenticated());
        }

        if (event is _CheckAuthStatus) {
          emit(const _Loading());

          // TODO: Implement actual auth status check (check stored tokens, etc.)
          emit(const _Unauthenticated());
        }
      } catch (e) {
        emit(_Error('An unexpected error occurred: ${e.toString()}'));
      }
    });
  }
}
