import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:recallloop/features/auth/data/model/login/login_model.dart';
import 'package:recallloop/features/auth/data/model/signup/signup_model.dart';
import 'package:recallloop/features/auth/data/services/login_service.dart';
import 'package:recallloop/features/auth/data/services/signup_service.dart';
import 'package:recallloop/core/services/local_user_service.dart';

part 'auth_bloc.freezed.dart';
part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LocalUserService _localUserService = LocalUserService();

  AuthBloc() : super(const _Initial()) {
    on<AuthEvent>((event, emit) async {
      try {
        if (event is _LoginRequested) {
          emit(const _Loading());

          final loginService = LoginService();
          final loginModel = await loginService.login(
            event.email,
            event.password,
          );

          if (loginModel != null) {
            // Save user data to local SQLite database
            final saveSuccess = await _localUserService.saveUserAfterLogin(
              loginModel,
            );
            if (saveSuccess) {
              print('User data saved to local database successfully');
            } else {
              print('Failed to save user data to local database');
            }

            emit(_Authenticated(loginUser: loginModel));
          } else {
            emit(_Error('Invalid email or password'));
          }
        }

        if (event is _SignUpRequested) {
          emit(const _Loading());
          print(event);
          final signupService = SignupService();
          final signupModel = await signupService.signup(
            event.email,
            event.password,
            event.firstName,
            event.lastName,
            event.phone,
            event.username,
          );
          print(signupModel);
          if (signupModel != null) {
            emit(_SignUpSuccess(signupUser: signupModel));
          } else {
            emit(
              _SignUpError(
                'Sign up failed. Please check your information and try again.',
              ),
            );
          }
        }

        if (event is _LogoutRequested) {
          emit(const _Loading());

          // Clear user data from local database
          final logoutSuccess = await _localUserService.logoutUser();
          if (logoutSuccess) {
            print('User logged out and local data cleared successfully');
          } else {
            print('Failed to clear local user data during logout');
          }

          emit(const _Unauthenticated());
        }

        if (event is _CheckAuthStatus) {
          emit(const _Loading());

          // Check if user is logged in with valid token
          final isLoggedIn = await _localUserService.isUserLoggedIn();
          if (isLoggedIn) {
            final currentUser = await _localUserService.getCurrentUser();
            if (currentUser != null) {
              // Create a LoginModel-like object from local user data
              // Note: This is a simplified approach. In a real app, you might want to
              // create a proper conversion method or use a different state
              print('User is authenticated with valid token');
              emit(
                const _Unauthenticated(),
              ); // For now, still emit unauthenticated
              // TODO: Create proper authenticated state from local user data
            } else {
              emit(const _Unauthenticated());
            }
          } else {
            emit(const _Unauthenticated());
          }
        }
      } catch (e) {
        emit(_Error('An unexpected error occurred: ${e.toString()}'));
      }
    });
  }
}
