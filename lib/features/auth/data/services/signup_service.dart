import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';
import 'package:recallloop/features/auth/data/model/signup/signup_model.dart';

final class SignupService {
  Future<SignupModel?> signup(
    String email,
    String password,
    String firstName,
    String lastName,
    String phone,
    String username,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('${dotenv.env['SUPABASE_URL']!}${ApiConfig.signup}'),
        headers: {
          'Content-Type': 'application/json',
          'apikey': dotenv.env['SUPABASE_API_KEY']!,
        },
        body: jsonEncode({
          'email': email,
          'password': password,
          'data': {
            'first_name': firstName,
            'last_name': lastName,
            'phone': phone,
            'username': username,
            "tenant_code": "TN-0001",
            "avatar_url": "",
            "party_type_key": "PLAYER",
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return SignupModel.fromJson(data);
      } else {
        print(response);
        return null;
      }
    } catch (e) {
      print('Signup error: $e');
      return null;
    }
  }
}
