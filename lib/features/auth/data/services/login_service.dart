import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';
import 'package:recallloop/features/auth/data/model/login/login_model.dart';

class LoginService {
  Future<LoginModel?> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('${dotenv.env['SUPABASE_URL']!}${ApiConfig.login}'),
        headers: {
          'Content-Type': 'application/json',
          'apikey': dotenv.env['SUPABASE_API_KEY']!,
        },
        body: jsonEncode({'email': email, 'password': password}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return LoginModel.fromJson(data);
      } else if (response.statusCode == 401) {
        print('Invalid credentials');
        return null;
      } else {
        return null;
      }
    } catch (e) {
      print('Login error: $e');
      return null;
    }
  }
}
