import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_model.freezed.dart';
part 'login_model.g.dart';

@freezed
class LoginModel with _$LoginModel {
  const factory LoginModel({
    @Json<PERSON>ey(name: "access_token") required String accessToken,
    @J<PERSON><PERSON><PERSON>(name: "token_type") required String tokenType,
    @Json<PERSON><PERSON>(name: "expires_in") required int expiresIn,
    @<PERSON>son<PERSON>ey(name: "expires_at") required int expiresAt,
    @<PERSON>son<PERSON>ey(name: "refresh_token") required String refreshToken,
    @<PERSON>son<PERSON><PERSON>(name: "user") required User user,
  }) = _LoginModel;

  factory LoginModel.fromJson(Map<String, dynamic> json) =>
      _$LoginModelFromJson(json);
}

@freezed
class User with _$User {
  const factory User({
    @Json<PERSON>ey(name: "id") required String id,
    @<PERSON>son<PERSON><PERSON>(name: "aud") required String aud,
    @<PERSON><PERSON><PERSON><PERSON>(name: "role") required String role,
    @<PERSON><PERSON><PERSON><PERSON>(name: "email") required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: "email_confirmed_at") required String emailConfirmedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: "phone") required String phone,
    @Json<PERSON>ey(name: "confirmed_at") required String confirmedAt,
    @JsonKey(name: "last_sign_in_at") required String lastSignInAt,
    @JsonKey(name: "app_metadata") required AppMetadata appMetadata,
    @JsonKey(name: "user_metadata") required Data userMetadata,
    @JsonKey(name: "identities") required List<Identity> identities,
    @JsonKey(name: "created_at") required String createdAt,
    @JsonKey(name: "updated_at") required String updatedAt,
    @JsonKey(name: "is_anonymous") required bool isAnonymous,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class AppMetadata with _$AppMetadata {
  const factory AppMetadata({
    @JsonKey(name: "provider") required String provider,
    @JsonKey(name: "providers") required List<String> providers,
  }) = _AppMetadata;

  factory AppMetadata.fromJson(Map<String, dynamic> json) =>
      _$AppMetadataFromJson(json);
}

@freezed
class Identity with _$Identity {
  const factory Identity({
    @JsonKey(name: "identity_id") required String identityId,
    @JsonKey(name: "id") required String id,
    @JsonKey(name: "user_id") required String userId,
    @JsonKey(name: "identity_data") required Data identityData,
    @JsonKey(name: "provider") required String provider,
    @JsonKey(name: "last_sign_in_at") required String lastSignInAt,
    @JsonKey(name: "created_at") required String createdAt,
    @JsonKey(name: "updated_at") required String updatedAt,
    @JsonKey(name: "email") required String email,
  }) = _Identity;

  factory Identity.fromJson(Map<String, dynamic> json) =>
      _$IdentityFromJson(json);
}

@freezed
class Data with _$Data {
  const factory Data({
    @JsonKey(name: "avatar_url") required String avatarUrl,
    @JsonKey(name: "email") required String email,
    @JsonKey(name: "email_verified") required bool emailVerified,
    @JsonKey(name: "first_name") required String firstName,
    @JsonKey(name: "last_name") required String lastName,
    @JsonKey(name: "party_type_key") required String partyTypeKey,
    @JsonKey(name: "phone") required String phone,
    @JsonKey(name: "phone_verified") required bool phoneVerified,
    @JsonKey(name: "sub") required String sub,
    @JsonKey(name: "tenant_code") required String tenantCode,
    @JsonKey(name: "username") required String username,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}
