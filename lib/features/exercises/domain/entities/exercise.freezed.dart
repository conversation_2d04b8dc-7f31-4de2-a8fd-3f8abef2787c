// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'exercise.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$Exercise {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  int get difficulty => throw _privateConstructorUsedError; // 1-5 scale
  int get estimatedDuration => throw _privateConstructorUsedError; // in minutes
  String get iconPath => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;
  int? get lastScore => throw _privateConstructorUsedError;
  DateTime? get lastPlayedAt => throw _privateConstructorUsedError;

  /// Create a copy of Exercise
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ExerciseCopyWith<Exercise> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExerciseCopyWith<$Res> {
  factory $ExerciseCopyWith(Exercise value, $Res Function(Exercise) then) =
      _$ExerciseCopyWithImpl<$Res, Exercise>;
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String type,
    int difficulty,
    int estimatedDuration,
    String iconPath,
    bool isCompleted,
    int? lastScore,
    DateTime? lastPlayedAt,
  });
}

/// @nodoc
class _$ExerciseCopyWithImpl<$Res, $Val extends Exercise>
    implements $ExerciseCopyWith<$Res> {
  _$ExerciseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Exercise
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? difficulty = null,
    Object? estimatedDuration = null,
    Object? iconPath = null,
    Object? isCompleted = null,
    Object? lastScore = freezed,
    Object? lastPlayedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            type: null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                      as String,
            difficulty: null == difficulty
                ? _value.difficulty
                : difficulty // ignore: cast_nullable_to_non_nullable
                      as int,
            estimatedDuration: null == estimatedDuration
                ? _value.estimatedDuration
                : estimatedDuration // ignore: cast_nullable_to_non_nullable
                      as int,
            iconPath: null == iconPath
                ? _value.iconPath
                : iconPath // ignore: cast_nullable_to_non_nullable
                      as String,
            isCompleted: null == isCompleted
                ? _value.isCompleted
                : isCompleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            lastScore: freezed == lastScore
                ? _value.lastScore
                : lastScore // ignore: cast_nullable_to_non_nullable
                      as int?,
            lastPlayedAt: freezed == lastPlayedAt
                ? _value.lastPlayedAt
                : lastPlayedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ExerciseImplCopyWith<$Res>
    implements $ExerciseCopyWith<$Res> {
  factory _$$ExerciseImplCopyWith(
    _$ExerciseImpl value,
    $Res Function(_$ExerciseImpl) then,
  ) = __$$ExerciseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String description,
    String type,
    int difficulty,
    int estimatedDuration,
    String iconPath,
    bool isCompleted,
    int? lastScore,
    DateTime? lastPlayedAt,
  });
}

/// @nodoc
class __$$ExerciseImplCopyWithImpl<$Res>
    extends _$ExerciseCopyWithImpl<$Res, _$ExerciseImpl>
    implements _$$ExerciseImplCopyWith<$Res> {
  __$$ExerciseImplCopyWithImpl(
    _$ExerciseImpl _value,
    $Res Function(_$ExerciseImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Exercise
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? type = null,
    Object? difficulty = null,
    Object? estimatedDuration = null,
    Object? iconPath = null,
    Object? isCompleted = null,
    Object? lastScore = freezed,
    Object? lastPlayedAt = freezed,
  }) {
    return _then(
      _$ExerciseImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        type: null == type
            ? _value.type
            : type // ignore: cast_nullable_to_non_nullable
                  as String,
        difficulty: null == difficulty
            ? _value.difficulty
            : difficulty // ignore: cast_nullable_to_non_nullable
                  as int,
        estimatedDuration: null == estimatedDuration
            ? _value.estimatedDuration
            : estimatedDuration // ignore: cast_nullable_to_non_nullable
                  as int,
        iconPath: null == iconPath
            ? _value.iconPath
            : iconPath // ignore: cast_nullable_to_non_nullable
                  as String,
        isCompleted: null == isCompleted
            ? _value.isCompleted
            : isCompleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        lastScore: freezed == lastScore
            ? _value.lastScore
            : lastScore // ignore: cast_nullable_to_non_nullable
                  as int?,
        lastPlayedAt: freezed == lastPlayedAt
            ? _value.lastPlayedAt
            : lastPlayedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc

class _$ExerciseImpl implements _Exercise {
  const _$ExerciseImpl({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.difficulty,
    required this.estimatedDuration,
    required this.iconPath,
    this.isCompleted = false,
    this.lastScore,
    this.lastPlayedAt,
  });

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String type;
  @override
  final int difficulty;
  // 1-5 scale
  @override
  final int estimatedDuration;
  // in minutes
  @override
  final String iconPath;
  @override
  @JsonKey()
  final bool isCompleted;
  @override
  final int? lastScore;
  @override
  final DateTime? lastPlayedAt;

  @override
  String toString() {
    return 'Exercise(id: $id, title: $title, description: $description, type: $type, difficulty: $difficulty, estimatedDuration: $estimatedDuration, iconPath: $iconPath, isCompleted: $isCompleted, lastScore: $lastScore, lastPlayedAt: $lastPlayedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExerciseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.estimatedDuration, estimatedDuration) ||
                other.estimatedDuration == estimatedDuration) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.lastScore, lastScore) ||
                other.lastScore == lastScore) &&
            (identical(other.lastPlayedAt, lastPlayedAt) ||
                other.lastPlayedAt == lastPlayedAt));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    description,
    type,
    difficulty,
    estimatedDuration,
    iconPath,
    isCompleted,
    lastScore,
    lastPlayedAt,
  );

  /// Create a copy of Exercise
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExerciseImplCopyWith<_$ExerciseImpl> get copyWith =>
      __$$ExerciseImplCopyWithImpl<_$ExerciseImpl>(this, _$identity);
}

abstract class _Exercise implements Exercise {
  const factory _Exercise({
    required final String id,
    required final String title,
    required final String description,
    required final String type,
    required final int difficulty,
    required final int estimatedDuration,
    required final String iconPath,
    final bool isCompleted,
    final int? lastScore,
    final DateTime? lastPlayedAt,
  }) = _$ExerciseImpl;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get type;
  @override
  int get difficulty; // 1-5 scale
  @override
  int get estimatedDuration; // in minutes
  @override
  String get iconPath;
  @override
  bool get isCompleted;
  @override
  int? get lastScore;
  @override
  DateTime? get lastPlayedAt;

  /// Create a copy of Exercise
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExerciseImplCopyWith<_$ExerciseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ExerciseResult {
  String get exerciseId => throw _privateConstructorUsedError;
  int get score => throw _privateConstructorUsedError;
  int get timeSpent => throw _privateConstructorUsedError; // in seconds
  DateTime get completedAt => throw _privateConstructorUsedError;
  Map<String, dynamic> get details => throw _privateConstructorUsedError;

  /// Create a copy of ExerciseResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ExerciseResultCopyWith<ExerciseResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExerciseResultCopyWith<$Res> {
  factory $ExerciseResultCopyWith(
    ExerciseResult value,
    $Res Function(ExerciseResult) then,
  ) = _$ExerciseResultCopyWithImpl<$Res, ExerciseResult>;
  @useResult
  $Res call({
    String exerciseId,
    int score,
    int timeSpent,
    DateTime completedAt,
    Map<String, dynamic> details,
  });
}

/// @nodoc
class _$ExerciseResultCopyWithImpl<$Res, $Val extends ExerciseResult>
    implements $ExerciseResultCopyWith<$Res> {
  _$ExerciseResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExerciseResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exerciseId = null,
    Object? score = null,
    Object? timeSpent = null,
    Object? completedAt = null,
    Object? details = null,
  }) {
    return _then(
      _value.copyWith(
            exerciseId: null == exerciseId
                ? _value.exerciseId
                : exerciseId // ignore: cast_nullable_to_non_nullable
                      as String,
            score: null == score
                ? _value.score
                : score // ignore: cast_nullable_to_non_nullable
                      as int,
            timeSpent: null == timeSpent
                ? _value.timeSpent
                : timeSpent // ignore: cast_nullable_to_non_nullable
                      as int,
            completedAt: null == completedAt
                ? _value.completedAt
                : completedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            details: null == details
                ? _value.details
                : details // ignore: cast_nullable_to_non_nullable
                      as Map<String, dynamic>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ExerciseResultImplCopyWith<$Res>
    implements $ExerciseResultCopyWith<$Res> {
  factory _$$ExerciseResultImplCopyWith(
    _$ExerciseResultImpl value,
    $Res Function(_$ExerciseResultImpl) then,
  ) = __$$ExerciseResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String exerciseId,
    int score,
    int timeSpent,
    DateTime completedAt,
    Map<String, dynamic> details,
  });
}

/// @nodoc
class __$$ExerciseResultImplCopyWithImpl<$Res>
    extends _$ExerciseResultCopyWithImpl<$Res, _$ExerciseResultImpl>
    implements _$$ExerciseResultImplCopyWith<$Res> {
  __$$ExerciseResultImplCopyWithImpl(
    _$ExerciseResultImpl _value,
    $Res Function(_$ExerciseResultImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExerciseResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exerciseId = null,
    Object? score = null,
    Object? timeSpent = null,
    Object? completedAt = null,
    Object? details = null,
  }) {
    return _then(
      _$ExerciseResultImpl(
        exerciseId: null == exerciseId
            ? _value.exerciseId
            : exerciseId // ignore: cast_nullable_to_non_nullable
                  as String,
        score: null == score
            ? _value.score
            : score // ignore: cast_nullable_to_non_nullable
                  as int,
        timeSpent: null == timeSpent
            ? _value.timeSpent
            : timeSpent // ignore: cast_nullable_to_non_nullable
                  as int,
        completedAt: null == completedAt
            ? _value.completedAt
            : completedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        details: null == details
            ? _value._details
            : details // ignore: cast_nullable_to_non_nullable
                  as Map<String, dynamic>,
      ),
    );
  }
}

/// @nodoc

class _$ExerciseResultImpl implements _ExerciseResult {
  const _$ExerciseResultImpl({
    required this.exerciseId,
    required this.score,
    required this.timeSpent,
    required this.completedAt,
    required final Map<String, dynamic> details,
  }) : _details = details;

  @override
  final String exerciseId;
  @override
  final int score;
  @override
  final int timeSpent;
  // in seconds
  @override
  final DateTime completedAt;
  final Map<String, dynamic> _details;
  @override
  Map<String, dynamic> get details {
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_details);
  }

  @override
  String toString() {
    return 'ExerciseResult(exerciseId: $exerciseId, score: $score, timeSpent: $timeSpent, completedAt: $completedAt, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExerciseResultImpl &&
            (identical(other.exerciseId, exerciseId) ||
                other.exerciseId == exerciseId) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.timeSpent, timeSpent) ||
                other.timeSpent == timeSpent) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    exerciseId,
    score,
    timeSpent,
    completedAt,
    const DeepCollectionEquality().hash(_details),
  );

  /// Create a copy of ExerciseResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExerciseResultImplCopyWith<_$ExerciseResultImpl> get copyWith =>
      __$$ExerciseResultImplCopyWithImpl<_$ExerciseResultImpl>(
        this,
        _$identity,
      );
}

abstract class _ExerciseResult implements ExerciseResult {
  const factory _ExerciseResult({
    required final String exerciseId,
    required final int score,
    required final int timeSpent,
    required final DateTime completedAt,
    required final Map<String, dynamic> details,
  }) = _$ExerciseResultImpl;

  @override
  String get exerciseId;
  @override
  int get score;
  @override
  int get timeSpent; // in seconds
  @override
  DateTime get completedAt;
  @override
  Map<String, dynamic> get details;

  /// Create a copy of ExerciseResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExerciseResultImplCopyWith<_$ExerciseResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
