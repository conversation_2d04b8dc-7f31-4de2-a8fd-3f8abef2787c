// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mood_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$MoodEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )
    entryAdded,
    required TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )
    entryUpdated,
    required TResult Function(String entryId) entryDeleted,
    required TResult Function(DateTime? startDate, DateTime? endDate)
    statsRequested,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult? Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult? Function(String entryId)? entryDeleted,
    TResult? Function(DateTime? startDate, DateTime? endDate)? statsRequested,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult Function(String entryId)? entryDeleted,
    TResult Function(DateTime? startDate, DateTime? endDate)? statsRequested,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_EntryAdded value) entryAdded,
    required TResult Function(_EntryUpdated value) entryUpdated,
    required TResult Function(_EntryDeleted value) entryDeleted,
    required TResult Function(_StatsRequested value) statsRequested,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_EntryAdded value)? entryAdded,
    TResult? Function(_EntryUpdated value)? entryUpdated,
    TResult? Function(_EntryDeleted value)? entryDeleted,
    TResult? Function(_StatsRequested value)? statsRequested,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_EntryAdded value)? entryAdded,
    TResult Function(_EntryUpdated value)? entryUpdated,
    TResult Function(_EntryDeleted value)? entryDeleted,
    TResult Function(_StatsRequested value)? statsRequested,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoodEventCopyWith<$Res> {
  factory $MoodEventCopyWith(MoodEvent value, $Res Function(MoodEvent) then) =
      _$MoodEventCopyWithImpl<$Res, MoodEvent>;
}

/// @nodoc
class _$MoodEventCopyWithImpl<$Res, $Val extends MoodEvent>
    implements $MoodEventCopyWith<$Res> {
  _$MoodEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadRequestedImplCopyWith<$Res> {
  factory _$$LoadRequestedImplCopyWith(
    _$LoadRequestedImpl value,
    $Res Function(_$LoadRequestedImpl) then,
  ) = __$$LoadRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadRequestedImplCopyWithImpl<$Res>
    extends _$MoodEventCopyWithImpl<$Res, _$LoadRequestedImpl>
    implements _$$LoadRequestedImplCopyWith<$Res> {
  __$$LoadRequestedImplCopyWithImpl(
    _$LoadRequestedImpl _value,
    $Res Function(_$LoadRequestedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadRequestedImpl implements _LoadRequested {
  const _$LoadRequestedImpl();

  @override
  String toString() {
    return 'MoodEvent.loadRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )
    entryAdded,
    required TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )
    entryUpdated,
    required TResult Function(String entryId) entryDeleted,
    required TResult Function(DateTime? startDate, DateTime? endDate)
    statsRequested,
  }) {
    return loadRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult? Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult? Function(String entryId)? entryDeleted,
    TResult? Function(DateTime? startDate, DateTime? endDate)? statsRequested,
  }) {
    return loadRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult Function(String entryId)? entryDeleted,
    TResult Function(DateTime? startDate, DateTime? endDate)? statsRequested,
    required TResult orElse(),
  }) {
    if (loadRequested != null) {
      return loadRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_EntryAdded value) entryAdded,
    required TResult Function(_EntryUpdated value) entryUpdated,
    required TResult Function(_EntryDeleted value) entryDeleted,
    required TResult Function(_StatsRequested value) statsRequested,
  }) {
    return loadRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_EntryAdded value)? entryAdded,
    TResult? Function(_EntryUpdated value)? entryUpdated,
    TResult? Function(_EntryDeleted value)? entryDeleted,
    TResult? Function(_StatsRequested value)? statsRequested,
  }) {
    return loadRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_EntryAdded value)? entryAdded,
    TResult Function(_EntryUpdated value)? entryUpdated,
    TResult Function(_EntryDeleted value)? entryDeleted,
    TResult Function(_StatsRequested value)? statsRequested,
    required TResult orElse(),
  }) {
    if (loadRequested != null) {
      return loadRequested(this);
    }
    return orElse();
  }
}

abstract class _LoadRequested implements MoodEvent {
  const factory _LoadRequested() = _$LoadRequestedImpl;
}

/// @nodoc
abstract class _$$EntryAddedImplCopyWith<$Res> {
  factory _$$EntryAddedImplCopyWith(
    _$EntryAddedImpl value,
    $Res Function(_$EntryAddedImpl) then,
  ) = __$$EntryAddedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    int moodLevel,
    String? note,
    List<String> tags,
    String? activityBefore,
  });
}

/// @nodoc
class __$$EntryAddedImplCopyWithImpl<$Res>
    extends _$MoodEventCopyWithImpl<$Res, _$EntryAddedImpl>
    implements _$$EntryAddedImplCopyWith<$Res> {
  __$$EntryAddedImplCopyWithImpl(
    _$EntryAddedImpl _value,
    $Res Function(_$EntryAddedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? moodLevel = null,
    Object? note = freezed,
    Object? tags = null,
    Object? activityBefore = freezed,
  }) {
    return _then(
      _$EntryAddedImpl(
        moodLevel: null == moodLevel
            ? _value.moodLevel
            : moodLevel // ignore: cast_nullable_to_non_nullable
                  as int,
        note: freezed == note
            ? _value.note
            : note // ignore: cast_nullable_to_non_nullable
                  as String?,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        activityBefore: freezed == activityBefore
            ? _value.activityBefore
            : activityBefore // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$EntryAddedImpl implements _EntryAdded {
  const _$EntryAddedImpl({
    required this.moodLevel,
    this.note,
    final List<String> tags = const [],
    this.activityBefore,
  }) : _tags = tags;

  @override
  final int moodLevel;
  @override
  final String? note;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final String? activityBefore;

  @override
  String toString() {
    return 'MoodEvent.entryAdded(moodLevel: $moodLevel, note: $note, tags: $tags, activityBefore: $activityBefore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EntryAddedImpl &&
            (identical(other.moodLevel, moodLevel) ||
                other.moodLevel == moodLevel) &&
            (identical(other.note, note) || other.note == note) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.activityBefore, activityBefore) ||
                other.activityBefore == activityBefore));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    moodLevel,
    note,
    const DeepCollectionEquality().hash(_tags),
    activityBefore,
  );

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EntryAddedImplCopyWith<_$EntryAddedImpl> get copyWith =>
      __$$EntryAddedImplCopyWithImpl<_$EntryAddedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )
    entryAdded,
    required TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )
    entryUpdated,
    required TResult Function(String entryId) entryDeleted,
    required TResult Function(DateTime? startDate, DateTime? endDate)
    statsRequested,
  }) {
    return entryAdded(moodLevel, note, tags, activityBefore);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult? Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult? Function(String entryId)? entryDeleted,
    TResult? Function(DateTime? startDate, DateTime? endDate)? statsRequested,
  }) {
    return entryAdded?.call(moodLevel, note, tags, activityBefore);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult Function(String entryId)? entryDeleted,
    TResult Function(DateTime? startDate, DateTime? endDate)? statsRequested,
    required TResult orElse(),
  }) {
    if (entryAdded != null) {
      return entryAdded(moodLevel, note, tags, activityBefore);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_EntryAdded value) entryAdded,
    required TResult Function(_EntryUpdated value) entryUpdated,
    required TResult Function(_EntryDeleted value) entryDeleted,
    required TResult Function(_StatsRequested value) statsRequested,
  }) {
    return entryAdded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_EntryAdded value)? entryAdded,
    TResult? Function(_EntryUpdated value)? entryUpdated,
    TResult? Function(_EntryDeleted value)? entryDeleted,
    TResult? Function(_StatsRequested value)? statsRequested,
  }) {
    return entryAdded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_EntryAdded value)? entryAdded,
    TResult Function(_EntryUpdated value)? entryUpdated,
    TResult Function(_EntryDeleted value)? entryDeleted,
    TResult Function(_StatsRequested value)? statsRequested,
    required TResult orElse(),
  }) {
    if (entryAdded != null) {
      return entryAdded(this);
    }
    return orElse();
  }
}

abstract class _EntryAdded implements MoodEvent {
  const factory _EntryAdded({
    required final int moodLevel,
    final String? note,
    final List<String> tags,
    final String? activityBefore,
  }) = _$EntryAddedImpl;

  int get moodLevel;
  String? get note;
  List<String> get tags;
  String? get activityBefore;

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EntryAddedImplCopyWith<_$EntryAddedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EntryUpdatedImplCopyWith<$Res> {
  factory _$$EntryUpdatedImplCopyWith(
    _$EntryUpdatedImpl value,
    $Res Function(_$EntryUpdatedImpl) then,
  ) = __$$EntryUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    String entryId,
    int? moodLevel,
    String? note,
    List<String>? tags,
    String? activityBefore,
  });
}

/// @nodoc
class __$$EntryUpdatedImplCopyWithImpl<$Res>
    extends _$MoodEventCopyWithImpl<$Res, _$EntryUpdatedImpl>
    implements _$$EntryUpdatedImplCopyWith<$Res> {
  __$$EntryUpdatedImplCopyWithImpl(
    _$EntryUpdatedImpl _value,
    $Res Function(_$EntryUpdatedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? entryId = null,
    Object? moodLevel = freezed,
    Object? note = freezed,
    Object? tags = freezed,
    Object? activityBefore = freezed,
  }) {
    return _then(
      _$EntryUpdatedImpl(
        entryId: null == entryId
            ? _value.entryId
            : entryId // ignore: cast_nullable_to_non_nullable
                  as String,
        moodLevel: freezed == moodLevel
            ? _value.moodLevel
            : moodLevel // ignore: cast_nullable_to_non_nullable
                  as int?,
        note: freezed == note
            ? _value.note
            : note // ignore: cast_nullable_to_non_nullable
                  as String?,
        tags: freezed == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
        activityBefore: freezed == activityBefore
            ? _value.activityBefore
            : activityBefore // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$EntryUpdatedImpl implements _EntryUpdated {
  const _$EntryUpdatedImpl({
    required this.entryId,
    this.moodLevel,
    this.note,
    final List<String>? tags,
    this.activityBefore,
  }) : _tags = tags;

  @override
  final String entryId;
  @override
  final int? moodLevel;
  @override
  final String? note;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? activityBefore;

  @override
  String toString() {
    return 'MoodEvent.entryUpdated(entryId: $entryId, moodLevel: $moodLevel, note: $note, tags: $tags, activityBefore: $activityBefore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EntryUpdatedImpl &&
            (identical(other.entryId, entryId) || other.entryId == entryId) &&
            (identical(other.moodLevel, moodLevel) ||
                other.moodLevel == moodLevel) &&
            (identical(other.note, note) || other.note == note) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.activityBefore, activityBefore) ||
                other.activityBefore == activityBefore));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    entryId,
    moodLevel,
    note,
    const DeepCollectionEquality().hash(_tags),
    activityBefore,
  );

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EntryUpdatedImplCopyWith<_$EntryUpdatedImpl> get copyWith =>
      __$$EntryUpdatedImplCopyWithImpl<_$EntryUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )
    entryAdded,
    required TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )
    entryUpdated,
    required TResult Function(String entryId) entryDeleted,
    required TResult Function(DateTime? startDate, DateTime? endDate)
    statsRequested,
  }) {
    return entryUpdated(entryId, moodLevel, note, tags, activityBefore);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult? Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult? Function(String entryId)? entryDeleted,
    TResult? Function(DateTime? startDate, DateTime? endDate)? statsRequested,
  }) {
    return entryUpdated?.call(entryId, moodLevel, note, tags, activityBefore);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult Function(String entryId)? entryDeleted,
    TResult Function(DateTime? startDate, DateTime? endDate)? statsRequested,
    required TResult orElse(),
  }) {
    if (entryUpdated != null) {
      return entryUpdated(entryId, moodLevel, note, tags, activityBefore);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_EntryAdded value) entryAdded,
    required TResult Function(_EntryUpdated value) entryUpdated,
    required TResult Function(_EntryDeleted value) entryDeleted,
    required TResult Function(_StatsRequested value) statsRequested,
  }) {
    return entryUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_EntryAdded value)? entryAdded,
    TResult? Function(_EntryUpdated value)? entryUpdated,
    TResult? Function(_EntryDeleted value)? entryDeleted,
    TResult? Function(_StatsRequested value)? statsRequested,
  }) {
    return entryUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_EntryAdded value)? entryAdded,
    TResult Function(_EntryUpdated value)? entryUpdated,
    TResult Function(_EntryDeleted value)? entryDeleted,
    TResult Function(_StatsRequested value)? statsRequested,
    required TResult orElse(),
  }) {
    if (entryUpdated != null) {
      return entryUpdated(this);
    }
    return orElse();
  }
}

abstract class _EntryUpdated implements MoodEvent {
  const factory _EntryUpdated({
    required final String entryId,
    final int? moodLevel,
    final String? note,
    final List<String>? tags,
    final String? activityBefore,
  }) = _$EntryUpdatedImpl;

  String get entryId;
  int? get moodLevel;
  String? get note;
  List<String>? get tags;
  String? get activityBefore;

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EntryUpdatedImplCopyWith<_$EntryUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EntryDeletedImplCopyWith<$Res> {
  factory _$$EntryDeletedImplCopyWith(
    _$EntryDeletedImpl value,
    $Res Function(_$EntryDeletedImpl) then,
  ) = __$$EntryDeletedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String entryId});
}

/// @nodoc
class __$$EntryDeletedImplCopyWithImpl<$Res>
    extends _$MoodEventCopyWithImpl<$Res, _$EntryDeletedImpl>
    implements _$$EntryDeletedImplCopyWith<$Res> {
  __$$EntryDeletedImplCopyWithImpl(
    _$EntryDeletedImpl _value,
    $Res Function(_$EntryDeletedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? entryId = null}) {
    return _then(
      _$EntryDeletedImpl(
        null == entryId
            ? _value.entryId
            : entryId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$EntryDeletedImpl implements _EntryDeleted {
  const _$EntryDeletedImpl(this.entryId);

  @override
  final String entryId;

  @override
  String toString() {
    return 'MoodEvent.entryDeleted(entryId: $entryId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EntryDeletedImpl &&
            (identical(other.entryId, entryId) || other.entryId == entryId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, entryId);

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EntryDeletedImplCopyWith<_$EntryDeletedImpl> get copyWith =>
      __$$EntryDeletedImplCopyWithImpl<_$EntryDeletedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )
    entryAdded,
    required TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )
    entryUpdated,
    required TResult Function(String entryId) entryDeleted,
    required TResult Function(DateTime? startDate, DateTime? endDate)
    statsRequested,
  }) {
    return entryDeleted(entryId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult? Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult? Function(String entryId)? entryDeleted,
    TResult? Function(DateTime? startDate, DateTime? endDate)? statsRequested,
  }) {
    return entryDeleted?.call(entryId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult Function(String entryId)? entryDeleted,
    TResult Function(DateTime? startDate, DateTime? endDate)? statsRequested,
    required TResult orElse(),
  }) {
    if (entryDeleted != null) {
      return entryDeleted(entryId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_EntryAdded value) entryAdded,
    required TResult Function(_EntryUpdated value) entryUpdated,
    required TResult Function(_EntryDeleted value) entryDeleted,
    required TResult Function(_StatsRequested value) statsRequested,
  }) {
    return entryDeleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_EntryAdded value)? entryAdded,
    TResult? Function(_EntryUpdated value)? entryUpdated,
    TResult? Function(_EntryDeleted value)? entryDeleted,
    TResult? Function(_StatsRequested value)? statsRequested,
  }) {
    return entryDeleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_EntryAdded value)? entryAdded,
    TResult Function(_EntryUpdated value)? entryUpdated,
    TResult Function(_EntryDeleted value)? entryDeleted,
    TResult Function(_StatsRequested value)? statsRequested,
    required TResult orElse(),
  }) {
    if (entryDeleted != null) {
      return entryDeleted(this);
    }
    return orElse();
  }
}

abstract class _EntryDeleted implements MoodEvent {
  const factory _EntryDeleted(final String entryId) = _$EntryDeletedImpl;

  String get entryId;

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EntryDeletedImplCopyWith<_$EntryDeletedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StatsRequestedImplCopyWith<$Res> {
  factory _$$StatsRequestedImplCopyWith(
    _$StatsRequestedImpl value,
    $Res Function(_$StatsRequestedImpl) then,
  ) = __$$StatsRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime? startDate, DateTime? endDate});
}

/// @nodoc
class __$$StatsRequestedImplCopyWithImpl<$Res>
    extends _$MoodEventCopyWithImpl<$Res, _$StatsRequestedImpl>
    implements _$$StatsRequestedImplCopyWith<$Res> {
  __$$StatsRequestedImplCopyWithImpl(
    _$StatsRequestedImpl _value,
    $Res Function(_$StatsRequestedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? startDate = freezed, Object? endDate = freezed}) {
    return _then(
      _$StatsRequestedImpl(
        startDate: freezed == startDate
            ? _value.startDate
            : startDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        endDate: freezed == endDate
            ? _value.endDate
            : endDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc

class _$StatsRequestedImpl implements _StatsRequested {
  const _$StatsRequestedImpl({this.startDate, this.endDate});

  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;

  @override
  String toString() {
    return 'MoodEvent.statsRequested(startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatsRequestedImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate, endDate);

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatsRequestedImplCopyWith<_$StatsRequestedImpl> get copyWith =>
      __$$StatsRequestedImplCopyWithImpl<_$StatsRequestedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )
    entryAdded,
    required TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )
    entryUpdated,
    required TResult Function(String entryId) entryDeleted,
    required TResult Function(DateTime? startDate, DateTime? endDate)
    statsRequested,
  }) {
    return statsRequested(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult? Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult? Function(String entryId)? entryDeleted,
    TResult? Function(DateTime? startDate, DateTime? endDate)? statsRequested,
  }) {
    return statsRequested?.call(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(
      int moodLevel,
      String? note,
      List<String> tags,
      String? activityBefore,
    )?
    entryAdded,
    TResult Function(
      String entryId,
      int? moodLevel,
      String? note,
      List<String>? tags,
      String? activityBefore,
    )?
    entryUpdated,
    TResult Function(String entryId)? entryDeleted,
    TResult Function(DateTime? startDate, DateTime? endDate)? statsRequested,
    required TResult orElse(),
  }) {
    if (statsRequested != null) {
      return statsRequested(startDate, endDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_EntryAdded value) entryAdded,
    required TResult Function(_EntryUpdated value) entryUpdated,
    required TResult Function(_EntryDeleted value) entryDeleted,
    required TResult Function(_StatsRequested value) statsRequested,
  }) {
    return statsRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_EntryAdded value)? entryAdded,
    TResult? Function(_EntryUpdated value)? entryUpdated,
    TResult? Function(_EntryDeleted value)? entryDeleted,
    TResult? Function(_StatsRequested value)? statsRequested,
  }) {
    return statsRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_EntryAdded value)? entryAdded,
    TResult Function(_EntryUpdated value)? entryUpdated,
    TResult Function(_EntryDeleted value)? entryDeleted,
    TResult Function(_StatsRequested value)? statsRequested,
    required TResult orElse(),
  }) {
    if (statsRequested != null) {
      return statsRequested(this);
    }
    return orElse();
  }
}

abstract class _StatsRequested implements MoodEvent {
  const factory _StatsRequested({
    final DateTime? startDate,
    final DateTime? endDate,
  }) = _$StatsRequestedImpl;

  DateTime? get startDate;
  DateTime? get endDate;

  /// Create a copy of MoodEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatsRequestedImplCopyWith<_$StatsRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MoodState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MoodEntryModel> entries, MoodStats stats)
    loaded,
    required TResult Function() entrySubmitting,
    required TResult Function(MoodEntryModel entry) entrySubmitted,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult? Function()? entrySubmitting,
    TResult? Function(MoodEntryModel entry)? entrySubmitted,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult Function()? entrySubmitting,
    TResult Function(MoodEntryModel entry)? entrySubmitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_EntrySubmitting value) entrySubmitting,
    required TResult Function(_EntrySubmitted value) entrySubmitted,
    required TResult Function(_Error value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_EntrySubmitting value)? entrySubmitting,
    TResult? Function(_EntrySubmitted value)? entrySubmitted,
    TResult? Function(_Error value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_EntrySubmitting value)? entrySubmitting,
    TResult Function(_EntrySubmitted value)? entrySubmitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoodStateCopyWith<$Res> {
  factory $MoodStateCopyWith(MoodState value, $Res Function(MoodState) then) =
      _$MoodStateCopyWithImpl<$Res, MoodState>;
}

/// @nodoc
class _$MoodStateCopyWithImpl<$Res, $Val extends MoodState>
    implements $MoodStateCopyWith<$Res> {
  _$MoodStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
    _$InitialImpl value,
    $Res Function(_$InitialImpl) then,
  ) = __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$MoodStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
    _$InitialImpl _value,
    $Res Function(_$InitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'MoodState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MoodEntryModel> entries, MoodStats stats)
    loaded,
    required TResult Function() entrySubmitting,
    required TResult Function(MoodEntryModel entry) entrySubmitted,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult? Function()? entrySubmitting,
    TResult? Function(MoodEntryModel entry)? entrySubmitted,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult Function()? entrySubmitting,
    TResult Function(MoodEntryModel entry)? entrySubmitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_EntrySubmitting value) entrySubmitting,
    required TResult Function(_EntrySubmitted value) entrySubmitted,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_EntrySubmitting value)? entrySubmitting,
    TResult? Function(_EntrySubmitted value)? entrySubmitted,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_EntrySubmitting value)? entrySubmitting,
    TResult Function(_EntrySubmitted value)? entrySubmitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements MoodState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
    _$LoadingImpl value,
    $Res Function(_$LoadingImpl) then,
  ) = __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$MoodStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
    _$LoadingImpl _value,
    $Res Function(_$LoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'MoodState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MoodEntryModel> entries, MoodStats stats)
    loaded,
    required TResult Function() entrySubmitting,
    required TResult Function(MoodEntryModel entry) entrySubmitted,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult? Function()? entrySubmitting,
    TResult? Function(MoodEntryModel entry)? entrySubmitted,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult Function()? entrySubmitting,
    TResult Function(MoodEntryModel entry)? entrySubmitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_EntrySubmitting value) entrySubmitting,
    required TResult Function(_EntrySubmitted value) entrySubmitted,
    required TResult Function(_Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_EntrySubmitting value)? entrySubmitting,
    TResult? Function(_EntrySubmitted value)? entrySubmitted,
    TResult? Function(_Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_EntrySubmitting value)? entrySubmitting,
    TResult Function(_EntrySubmitted value)? entrySubmitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements MoodState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
    _$LoadedImpl value,
    $Res Function(_$LoadedImpl) then,
  ) = __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<MoodEntryModel> entries, MoodStats stats});

  $MoodStatsCopyWith<$Res> get stats;
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$MoodStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
    _$LoadedImpl _value,
    $Res Function(_$LoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? entries = null, Object? stats = null}) {
    return _then(
      _$LoadedImpl(
        entries: null == entries
            ? _value._entries
            : entries // ignore: cast_nullable_to_non_nullable
                  as List<MoodEntryModel>,
        stats: null == stats
            ? _value.stats
            : stats // ignore: cast_nullable_to_non_nullable
                  as MoodStats,
      ),
    );
  }

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MoodStatsCopyWith<$Res> get stats {
    return $MoodStatsCopyWith<$Res>(_value.stats, (value) {
      return _then(_value.copyWith(stats: value));
    });
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl({
    required final List<MoodEntryModel> entries,
    required this.stats,
  }) : _entries = entries;

  final List<MoodEntryModel> _entries;
  @override
  List<MoodEntryModel> get entries {
    if (_entries is EqualUnmodifiableListView) return _entries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_entries);
  }

  @override
  final MoodStats stats;

  @override
  String toString() {
    return 'MoodState.loaded(entries: $entries, stats: $stats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(other._entries, _entries) &&
            (identical(other.stats, stats) || other.stats == stats));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_entries),
    stats,
  );

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MoodEntryModel> entries, MoodStats stats)
    loaded,
    required TResult Function() entrySubmitting,
    required TResult Function(MoodEntryModel entry) entrySubmitted,
    required TResult Function(String message) error,
  }) {
    return loaded(entries, stats);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult? Function()? entrySubmitting,
    TResult? Function(MoodEntryModel entry)? entrySubmitted,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(entries, stats);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult Function()? entrySubmitting,
    TResult Function(MoodEntryModel entry)? entrySubmitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(entries, stats);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_EntrySubmitting value) entrySubmitting,
    required TResult Function(_EntrySubmitted value) entrySubmitted,
    required TResult Function(_Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_EntrySubmitting value)? entrySubmitting,
    TResult? Function(_EntrySubmitted value)? entrySubmitted,
    TResult? Function(_Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_EntrySubmitting value)? entrySubmitting,
    TResult Function(_EntrySubmitted value)? entrySubmitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements MoodState {
  const factory _Loaded({
    required final List<MoodEntryModel> entries,
    required final MoodStats stats,
  }) = _$LoadedImpl;

  List<MoodEntryModel> get entries;
  MoodStats get stats;

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EntrySubmittingImplCopyWith<$Res> {
  factory _$$EntrySubmittingImplCopyWith(
    _$EntrySubmittingImpl value,
    $Res Function(_$EntrySubmittingImpl) then,
  ) = __$$EntrySubmittingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EntrySubmittingImplCopyWithImpl<$Res>
    extends _$MoodStateCopyWithImpl<$Res, _$EntrySubmittingImpl>
    implements _$$EntrySubmittingImplCopyWith<$Res> {
  __$$EntrySubmittingImplCopyWithImpl(
    _$EntrySubmittingImpl _value,
    $Res Function(_$EntrySubmittingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EntrySubmittingImpl implements _EntrySubmitting {
  const _$EntrySubmittingImpl();

  @override
  String toString() {
    return 'MoodState.entrySubmitting()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EntrySubmittingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MoodEntryModel> entries, MoodStats stats)
    loaded,
    required TResult Function() entrySubmitting,
    required TResult Function(MoodEntryModel entry) entrySubmitted,
    required TResult Function(String message) error,
  }) {
    return entrySubmitting();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult? Function()? entrySubmitting,
    TResult? Function(MoodEntryModel entry)? entrySubmitted,
    TResult? Function(String message)? error,
  }) {
    return entrySubmitting?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult Function()? entrySubmitting,
    TResult Function(MoodEntryModel entry)? entrySubmitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (entrySubmitting != null) {
      return entrySubmitting();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_EntrySubmitting value) entrySubmitting,
    required TResult Function(_EntrySubmitted value) entrySubmitted,
    required TResult Function(_Error value) error,
  }) {
    return entrySubmitting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_EntrySubmitting value)? entrySubmitting,
    TResult? Function(_EntrySubmitted value)? entrySubmitted,
    TResult? Function(_Error value)? error,
  }) {
    return entrySubmitting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_EntrySubmitting value)? entrySubmitting,
    TResult Function(_EntrySubmitted value)? entrySubmitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (entrySubmitting != null) {
      return entrySubmitting(this);
    }
    return orElse();
  }
}

abstract class _EntrySubmitting implements MoodState {
  const factory _EntrySubmitting() = _$EntrySubmittingImpl;
}

/// @nodoc
abstract class _$$EntrySubmittedImplCopyWith<$Res> {
  factory _$$EntrySubmittedImplCopyWith(
    _$EntrySubmittedImpl value,
    $Res Function(_$EntrySubmittedImpl) then,
  ) = __$$EntrySubmittedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MoodEntryModel entry});

  $MoodEntryModelCopyWith<$Res> get entry;
}

/// @nodoc
class __$$EntrySubmittedImplCopyWithImpl<$Res>
    extends _$MoodStateCopyWithImpl<$Res, _$EntrySubmittedImpl>
    implements _$$EntrySubmittedImplCopyWith<$Res> {
  __$$EntrySubmittedImplCopyWithImpl(
    _$EntrySubmittedImpl _value,
    $Res Function(_$EntrySubmittedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? entry = null}) {
    return _then(
      _$EntrySubmittedImpl(
        null == entry
            ? _value.entry
            : entry // ignore: cast_nullable_to_non_nullable
                  as MoodEntryModel,
      ),
    );
  }

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MoodEntryModelCopyWith<$Res> get entry {
    return $MoodEntryModelCopyWith<$Res>(_value.entry, (value) {
      return _then(_value.copyWith(entry: value));
    });
  }
}

/// @nodoc

class _$EntrySubmittedImpl implements _EntrySubmitted {
  const _$EntrySubmittedImpl(this.entry);

  @override
  final MoodEntryModel entry;

  @override
  String toString() {
    return 'MoodState.entrySubmitted(entry: $entry)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EntrySubmittedImpl &&
            (identical(other.entry, entry) || other.entry == entry));
  }

  @override
  int get hashCode => Object.hash(runtimeType, entry);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EntrySubmittedImplCopyWith<_$EntrySubmittedImpl> get copyWith =>
      __$$EntrySubmittedImplCopyWithImpl<_$EntrySubmittedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MoodEntryModel> entries, MoodStats stats)
    loaded,
    required TResult Function() entrySubmitting,
    required TResult Function(MoodEntryModel entry) entrySubmitted,
    required TResult Function(String message) error,
  }) {
    return entrySubmitted(entry);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult? Function()? entrySubmitting,
    TResult? Function(MoodEntryModel entry)? entrySubmitted,
    TResult? Function(String message)? error,
  }) {
    return entrySubmitted?.call(entry);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult Function()? entrySubmitting,
    TResult Function(MoodEntryModel entry)? entrySubmitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (entrySubmitted != null) {
      return entrySubmitted(entry);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_EntrySubmitting value) entrySubmitting,
    required TResult Function(_EntrySubmitted value) entrySubmitted,
    required TResult Function(_Error value) error,
  }) {
    return entrySubmitted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_EntrySubmitting value)? entrySubmitting,
    TResult? Function(_EntrySubmitted value)? entrySubmitted,
    TResult? Function(_Error value)? error,
  }) {
    return entrySubmitted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_EntrySubmitting value)? entrySubmitting,
    TResult Function(_EntrySubmitted value)? entrySubmitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (entrySubmitted != null) {
      return entrySubmitted(this);
    }
    return orElse();
  }
}

abstract class _EntrySubmitted implements MoodState {
  const factory _EntrySubmitted(final MoodEntryModel entry) =
      _$EntrySubmittedImpl;

  MoodEntryModel get entry;

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EntrySubmittedImplCopyWith<_$EntrySubmittedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
    _$ErrorImpl value,
    $Res Function(_$ErrorImpl) then,
  ) = __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$MoodStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
    _$ErrorImpl _value,
    $Res Function(_$ErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$ErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MoodState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MoodEntryModel> entries, MoodStats stats)
    loaded,
    required TResult Function() entrySubmitting,
    required TResult Function(MoodEntryModel entry) entrySubmitted,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult? Function()? entrySubmitting,
    TResult? Function(MoodEntryModel entry)? entrySubmitted,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MoodEntryModel> entries, MoodStats stats)? loaded,
    TResult Function()? entrySubmitting,
    TResult Function(MoodEntryModel entry)? entrySubmitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_EntrySubmitting value) entrySubmitting,
    required TResult Function(_EntrySubmitted value) entrySubmitted,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_EntrySubmitting value)? entrySubmitting,
    TResult? Function(_EntrySubmitted value)? entrySubmitted,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_EntrySubmitting value)? entrySubmitting,
    TResult Function(_EntrySubmitted value)? entrySubmitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements MoodState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of MoodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
