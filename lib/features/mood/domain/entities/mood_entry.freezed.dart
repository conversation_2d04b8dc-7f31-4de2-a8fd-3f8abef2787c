// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mood_entry.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$MoodEntryModel {
  String get id => throw _privateConstructorUsedError;
  int get moodLevel => throw _privateConstructorUsedError; // 1-5 scale
  String? get note => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  String? get activityBefore => throw _privateConstructorUsedError;

  /// Create a copy of MoodEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MoodEntryModelCopyWith<MoodEntryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoodEntryModelCopyWith<$Res> {
  factory $MoodEntryModelCopyWith(
    MoodEntryModel value,
    $Res Function(MoodEntryModel) then,
  ) = _$MoodEntryModelCopyWithImpl<$Res, MoodEntryModel>;
  @useResult
  $Res call({
    String id,
    int moodLevel,
    String? note,
    DateTime timestamp,
    List<String> tags,
    String? activityBefore,
  });
}

/// @nodoc
class _$MoodEntryModelCopyWithImpl<$Res, $Val extends MoodEntryModel>
    implements $MoodEntryModelCopyWith<$Res> {
  _$MoodEntryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MoodEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? moodLevel = null,
    Object? note = freezed,
    Object? timestamp = null,
    Object? tags = null,
    Object? activityBefore = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            moodLevel: null == moodLevel
                ? _value.moodLevel
                : moodLevel // ignore: cast_nullable_to_non_nullable
                      as int,
            note: freezed == note
                ? _value.note
                : note // ignore: cast_nullable_to_non_nullable
                      as String?,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            activityBefore: freezed == activityBefore
                ? _value.activityBefore
                : activityBefore // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MoodEntryModelImplCopyWith<$Res>
    implements $MoodEntryModelCopyWith<$Res> {
  factory _$$MoodEntryModelImplCopyWith(
    _$MoodEntryModelImpl value,
    $Res Function(_$MoodEntryModelImpl) then,
  ) = __$$MoodEntryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    int moodLevel,
    String? note,
    DateTime timestamp,
    List<String> tags,
    String? activityBefore,
  });
}

/// @nodoc
class __$$MoodEntryModelImplCopyWithImpl<$Res>
    extends _$MoodEntryModelCopyWithImpl<$Res, _$MoodEntryModelImpl>
    implements _$$MoodEntryModelImplCopyWith<$Res> {
  __$$MoodEntryModelImplCopyWithImpl(
    _$MoodEntryModelImpl _value,
    $Res Function(_$MoodEntryModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? moodLevel = null,
    Object? note = freezed,
    Object? timestamp = null,
    Object? tags = null,
    Object? activityBefore = freezed,
  }) {
    return _then(
      _$MoodEntryModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        moodLevel: null == moodLevel
            ? _value.moodLevel
            : moodLevel // ignore: cast_nullable_to_non_nullable
                  as int,
        note: freezed == note
            ? _value.note
            : note // ignore: cast_nullable_to_non_nullable
                  as String?,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        activityBefore: freezed == activityBefore
            ? _value.activityBefore
            : activityBefore // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$MoodEntryModelImpl implements _MoodEntryModel {
  const _$MoodEntryModelImpl({
    required this.id,
    required this.moodLevel,
    this.note,
    required this.timestamp,
    final List<String> tags = const [],
    this.activityBefore,
  }) : _tags = tags;

  @override
  final String id;
  @override
  final int moodLevel;
  // 1-5 scale
  @override
  final String? note;
  @override
  final DateTime timestamp;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final String? activityBefore;

  @override
  String toString() {
    return 'MoodEntryModel(id: $id, moodLevel: $moodLevel, note: $note, timestamp: $timestamp, tags: $tags, activityBefore: $activityBefore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MoodEntryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.moodLevel, moodLevel) ||
                other.moodLevel == moodLevel) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.activityBefore, activityBefore) ||
                other.activityBefore == activityBefore));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    moodLevel,
    note,
    timestamp,
    const DeepCollectionEquality().hash(_tags),
    activityBefore,
  );

  /// Create a copy of MoodEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MoodEntryModelImplCopyWith<_$MoodEntryModelImpl> get copyWith =>
      __$$MoodEntryModelImplCopyWithImpl<_$MoodEntryModelImpl>(
        this,
        _$identity,
      );
}

abstract class _MoodEntryModel implements MoodEntryModel {
  const factory _MoodEntryModel({
    required final String id,
    required final int moodLevel,
    final String? note,
    required final DateTime timestamp,
    final List<String> tags,
    final String? activityBefore,
  }) = _$MoodEntryModelImpl;

  @override
  String get id;
  @override
  int get moodLevel; // 1-5 scale
  @override
  String? get note;
  @override
  DateTime get timestamp;
  @override
  List<String> get tags;
  @override
  String? get activityBefore;

  /// Create a copy of MoodEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MoodEntryModelImplCopyWith<_$MoodEntryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MoodStats {
  double get averageMood => throw _privateConstructorUsedError;
  int get totalEntries => throw _privateConstructorUsedError;
  Map<int, int> get moodDistribution => throw _privateConstructorUsedError;
  List<String> get commonTags => throw _privateConstructorUsedError;
  DateTime? get lastEntryDate => throw _privateConstructorUsedError;

  /// Create a copy of MoodStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MoodStatsCopyWith<MoodStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoodStatsCopyWith<$Res> {
  factory $MoodStatsCopyWith(MoodStats value, $Res Function(MoodStats) then) =
      _$MoodStatsCopyWithImpl<$Res, MoodStats>;
  @useResult
  $Res call({
    double averageMood,
    int totalEntries,
    Map<int, int> moodDistribution,
    List<String> commonTags,
    DateTime? lastEntryDate,
  });
}

/// @nodoc
class _$MoodStatsCopyWithImpl<$Res, $Val extends MoodStats>
    implements $MoodStatsCopyWith<$Res> {
  _$MoodStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MoodStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? averageMood = null,
    Object? totalEntries = null,
    Object? moodDistribution = null,
    Object? commonTags = null,
    Object? lastEntryDate = freezed,
  }) {
    return _then(
      _value.copyWith(
            averageMood: null == averageMood
                ? _value.averageMood
                : averageMood // ignore: cast_nullable_to_non_nullable
                      as double,
            totalEntries: null == totalEntries
                ? _value.totalEntries
                : totalEntries // ignore: cast_nullable_to_non_nullable
                      as int,
            moodDistribution: null == moodDistribution
                ? _value.moodDistribution
                : moodDistribution // ignore: cast_nullable_to_non_nullable
                      as Map<int, int>,
            commonTags: null == commonTags
                ? _value.commonTags
                : commonTags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            lastEntryDate: freezed == lastEntryDate
                ? _value.lastEntryDate
                : lastEntryDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MoodStatsImplCopyWith<$Res>
    implements $MoodStatsCopyWith<$Res> {
  factory _$$MoodStatsImplCopyWith(
    _$MoodStatsImpl value,
    $Res Function(_$MoodStatsImpl) then,
  ) = __$$MoodStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    double averageMood,
    int totalEntries,
    Map<int, int> moodDistribution,
    List<String> commonTags,
    DateTime? lastEntryDate,
  });
}

/// @nodoc
class __$$MoodStatsImplCopyWithImpl<$Res>
    extends _$MoodStatsCopyWithImpl<$Res, _$MoodStatsImpl>
    implements _$$MoodStatsImplCopyWith<$Res> {
  __$$MoodStatsImplCopyWithImpl(
    _$MoodStatsImpl _value,
    $Res Function(_$MoodStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? averageMood = null,
    Object? totalEntries = null,
    Object? moodDistribution = null,
    Object? commonTags = null,
    Object? lastEntryDate = freezed,
  }) {
    return _then(
      _$MoodStatsImpl(
        averageMood: null == averageMood
            ? _value.averageMood
            : averageMood // ignore: cast_nullable_to_non_nullable
                  as double,
        totalEntries: null == totalEntries
            ? _value.totalEntries
            : totalEntries // ignore: cast_nullable_to_non_nullable
                  as int,
        moodDistribution: null == moodDistribution
            ? _value._moodDistribution
            : moodDistribution // ignore: cast_nullable_to_non_nullable
                  as Map<int, int>,
        commonTags: null == commonTags
            ? _value._commonTags
            : commonTags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        lastEntryDate: freezed == lastEntryDate
            ? _value.lastEntryDate
            : lastEntryDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc

class _$MoodStatsImpl implements _MoodStats {
  const _$MoodStatsImpl({
    required this.averageMood,
    required this.totalEntries,
    required final Map<int, int> moodDistribution,
    required final List<String> commonTags,
    this.lastEntryDate,
  }) : _moodDistribution = moodDistribution,
       _commonTags = commonTags;

  @override
  final double averageMood;
  @override
  final int totalEntries;
  final Map<int, int> _moodDistribution;
  @override
  Map<int, int> get moodDistribution {
    if (_moodDistribution is EqualUnmodifiableMapView) return _moodDistribution;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_moodDistribution);
  }

  final List<String> _commonTags;
  @override
  List<String> get commonTags {
    if (_commonTags is EqualUnmodifiableListView) return _commonTags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_commonTags);
  }

  @override
  final DateTime? lastEntryDate;

  @override
  String toString() {
    return 'MoodStats(averageMood: $averageMood, totalEntries: $totalEntries, moodDistribution: $moodDistribution, commonTags: $commonTags, lastEntryDate: $lastEntryDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MoodStatsImpl &&
            (identical(other.averageMood, averageMood) ||
                other.averageMood == averageMood) &&
            (identical(other.totalEntries, totalEntries) ||
                other.totalEntries == totalEntries) &&
            const DeepCollectionEquality().equals(
              other._moodDistribution,
              _moodDistribution,
            ) &&
            const DeepCollectionEquality().equals(
              other._commonTags,
              _commonTags,
            ) &&
            (identical(other.lastEntryDate, lastEntryDate) ||
                other.lastEntryDate == lastEntryDate));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    averageMood,
    totalEntries,
    const DeepCollectionEquality().hash(_moodDistribution),
    const DeepCollectionEquality().hash(_commonTags),
    lastEntryDate,
  );

  /// Create a copy of MoodStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MoodStatsImplCopyWith<_$MoodStatsImpl> get copyWith =>
      __$$MoodStatsImplCopyWithImpl<_$MoodStatsImpl>(this, _$identity);
}

abstract class _MoodStats implements MoodStats {
  const factory _MoodStats({
    required final double averageMood,
    required final int totalEntries,
    required final Map<int, int> moodDistribution,
    required final List<String> commonTags,
    final DateTime? lastEntryDate,
  }) = _$MoodStatsImpl;

  @override
  double get averageMood;
  @override
  int get totalEntries;
  @override
  Map<int, int> get moodDistribution;
  @override
  List<String> get commonTags;
  @override
  DateTime? get lastEntryDate;

  /// Create a copy of MoodStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MoodStatsImplCopyWith<_$MoodStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
