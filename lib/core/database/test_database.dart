import 'package:flutter/foundation.dart';
import '../services/local_user_service.dart';
import 'entities/local_user_entity.dart';

/// Test class to verify SQLite database functionality
/// This should only be used in development/debug mode
class DatabaseTester {
  static final LocalUserService _localUserService = LocalUserService();

  /// Test basic database operations
  static Future<void> testDatabaseOperations() async {
    if (!kDebugMode) {
      print('Database testing is only available in debug mode');
      return;
    }

    print('🧪 Starting database tests...');

    try {
      // Test 1: Clear any existing data
      print('\n1. Clearing existing data...');
      await _localUserService.clearAllUserData();
      final initialCount = await _localUserService.getUserCount();
      print('Initial user count: $initialCount');

      // Test 2: Create a mock user
      print('\n2. Creating mock user...');
      final mockUser = _createMockUser();
      print('Mock user created: ${mockUser.email}');

      // Test 3: Save user (simulate login)
      print('\n3. Saving user to database...');
      final mockLoginModel = _createMockLoginModel();
      final saveSuccess = await _localUserService.saveUserAfterLogin(mockLoginModel);
      print('Save success: $saveSuccess');

      // Test 4: Retrieve user
      print('\n4. Retrieving user...');
      final retrievedUser = await _localUserService.getCurrentUser();
      if (retrievedUser != null) {
        print('Retrieved user: ${retrievedUser.email}');
        print('User ID: ${retrievedUser.id}');
        print('Username: ${retrievedUser.username}');
        print('Full name: ${retrievedUser.firstName} ${retrievedUser.lastName}');
      } else {
        print('❌ Failed to retrieve user');
      }

      // Test 5: Check if user is logged in
      print('\n5. Checking login status...');
      final isLoggedIn = await _localUserService.isUserLoggedIn();
      print('Is logged in: $isLoggedIn');

      // Test 6: Get user by email
      print('\n6. Getting user by email...');
      final userByEmail = await _localUserService.getUserByEmail('<EMAIL>');
      print('User by email found: ${userByEmail != null}');

      // Test 7: Update user
      print('\n7. Updating user...');
      if (retrievedUser != null) {
        final updatedUser = retrievedUser.copyWith(
          firstName: 'Updated',
          lastName: 'Name',
        );
        final updateSuccess = await _localUserService.updateUser(updatedUser);
        print('Update success: $updateSuccess');

        // Verify update
        final updatedRetrievedUser = await _localUserService.getCurrentUser();
        if (updatedRetrievedUser != null) {
          print('Updated name: ${updatedRetrievedUser.firstName} ${updatedRetrievedUser.lastName}');
        }
      }

      // Test 8: Get all users
      print('\n8. Getting all users...');
      final allUsers = await _localUserService.getAllUsers();
      print('Total users: ${allUsers.length}');

      // Test 9: User count
      print('\n9. Getting user count...');
      final userCount = await _localUserService.getUserCount();
      print('User count: $userCount');

      // Test 10: Logout (clear data)
      print('\n10. Testing logout...');
      final logoutSuccess = await _localUserService.logoutUser();
      print('Logout success: $logoutSuccess');

      final finalCount = await _localUserService.getUserCount();
      print('Final user count: $finalCount');

      print('\n✅ Database tests completed successfully!');
    } catch (e) {
      print('\n❌ Database test failed: $e');
    }
  }

  /// Create a mock LoginModel for testing
  static dynamic _createMockLoginModel() {
    // This is a simplified mock object that mimics the structure of LoginModel
    return MockLoginModel(
      accessToken: 'mock_access_token_123',
      tokenType: 'Bearer',
      expiresIn: 3600,
      expiresAt: (DateTime.now().millisecondsSinceEpoch ~/ 1000) + 3600,
      refreshToken: 'mock_refresh_token_456',
      user: MockUser(
        id: 'user_123',
        aud: 'authenticated',
        role: 'authenticated',
        email: '<EMAIL>',
        emailConfirmedAt: DateTime.now().toIso8601String(),
        phone: '+**********',
        confirmedAt: DateTime.now().toIso8601String(),
        lastSignInAt: DateTime.now().toIso8601String(),
        appMetadata: MockAppMetadata(
          provider: 'email',
          providers: ['email'],
        ),
        userMetadata: MockUserMetadata(
          avatarUrl: 'https://example.com/avatar.jpg',
          email: '<EMAIL>',
          emailVerified: true,
          firstName: 'Test',
          lastName: 'User',
          partyTypeKey: 'PLAYER',
          phone: '+**********',
          phoneVerified: true,
          sub: 'user_123',
          tenantCode: 'TN-0001',
          username: 'testuser',
        ),
        identities: [],
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
        isAnonymous: false,
      ),
    );
  }

  /// Create a mock LocalUserEntity for testing
  static LocalUserEntity _createMockUser() {
    final now = DateTime.now();
    return LocalUserEntity(
      id: 'test_user_123',
      email: '<EMAIL>',
      phone: '+**********',
      firstName: 'Test',
      lastName: 'User',
      username: 'testuser',
      avatarUrl: 'https://example.com/avatar.jpg',
      tenantCode: 'TN-0001',
      partyTypeKey: 'PLAYER',
      emailVerified: true,
      phoneVerified: true,
      accessToken: 'mock_access_token',
      refreshToken: 'mock_refresh_token',
      tokenType: 'Bearer',
      expiresIn: 3600,
      expiresAt: (now.millisecondsSinceEpoch ~/ 1000) + 3600,
      role: 'authenticated',
      aud: 'authenticated',
      emailConfirmedAt: now.toIso8601String(),
      confirmedAt: now.toIso8601String(),
      lastSignInAt: now.toIso8601String(),
      createdAt: now.toIso8601String(),
      updatedAt: now.toIso8601String(),
      isAnonymous: false,
      localCreatedAt: now,
      localUpdatedAt: now,
    );
  }
}

// Mock classes for testing
class MockLoginModel {
  final String accessToken;
  final String tokenType;
  final int expiresIn;
  final int expiresAt;
  final String refreshToken;
  final MockUser user;

  MockLoginModel({
    required this.accessToken,
    required this.tokenType,
    required this.expiresIn,
    required this.expiresAt,
    required this.refreshToken,
    required this.user,
  });
}

class MockUser {
  final String id;
  final String aud;
  final String role;
  final String email;
  final String emailConfirmedAt;
  final String phone;
  final String confirmedAt;
  final String lastSignInAt;
  final MockAppMetadata appMetadata;
  final MockUserMetadata userMetadata;
  final List<dynamic> identities;
  final String createdAt;
  final String updatedAt;
  final bool isAnonymous;

  MockUser({
    required this.id,
    required this.aud,
    required this.role,
    required this.email,
    required this.emailConfirmedAt,
    required this.phone,
    required this.confirmedAt,
    required this.lastSignInAt,
    required this.appMetadata,
    required this.userMetadata,
    required this.identities,
    required this.createdAt,
    required this.updatedAt,
    required this.isAnonymous,
  });
}

class MockAppMetadata {
  final String provider;
  final List<String> providers;

  MockAppMetadata({
    required this.provider,
    required this.providers,
  });
}

class MockUserMetadata {
  final String avatarUrl;
  final String email;
  final bool emailVerified;
  final String firstName;
  final String lastName;
  final String partyTypeKey;
  final String phone;
  final bool phoneVerified;
  final String sub;
  final String tenantCode;
  final String username;

  MockUserMetadata({
    required this.avatarUrl,
    required this.email,
    required this.emailVerified,
    required this.firstName,
    required this.lastName,
    required this.partyTypeKey,
    required this.phone,
    required this.phoneVerified,
    required this.sub,
    required this.tenantCode,
    required this.username,
  });
}
